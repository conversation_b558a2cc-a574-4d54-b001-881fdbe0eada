<?php

namespace App\Filament\Admin\Resources;

use App\Enums\Car\BodyType;
use App\Enums\Car\CylinderCount;
use App\Enums\Car\DrivetrainSystem;
use App\Enums\Car\ExteriorColor;
use App\Enums\Car\FeatureCategory;
use App\Enums\Car\FuelType;
use App\Enums\Car\ImageLabel;
use App\Enums\Car\InteriorColor;
use App\Enums\Car\RegionalSpecification;
use App\Enums\Car\Status;
use App\Enums\Car\TransmissionType;
use App\Enums\City;
use App\Enums\Currency;
use App\Filament\Admin\Resources\CarResource\Pages;
use App\Filament\Shared\Components\CarManufacturerSelect;
use App\Filament\Shared\Components\CarModelSelect;
use App\Models\Car;
use App\Models\Feature;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;

class CarResource extends Resource
{
    protected static ?string $model = Car::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return trans_choice('resources.car.label', 1);
    }

    public static function getPluralModelLabel(): string
    {
        return trans_choice('resources.car.label', 2);
    }

    public static function isSkippable(string $operation): bool
    {
        return app()->isLocal() || $operation === 'edit';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Wizard::make()
                    ->persistStepInQueryString()
                    ->skippable(self::isSkippable(...))
                    ->steps([
                        Forms\Components\Wizard\Step::make(__('resources.car.steps.basic_info'))
                            ->id('basic_info')
                            ->icon('heroicon-o-information-circle')
                            ->schema([
                                Forms\Components\Select::make('store_id')
                                    ->label(__('resources.car.fields.store'))
                                    ->relationship('store', 'name_' . app()->getLocale())
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                                CarManufacturerSelect::make('car_manufacturer_id')
                                    ->live()
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                                CarModelSelect::make('car_model_id')
                                    ->required()
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\Select::make('year')
                                    ->label(__('resources.car.fields.year'))
                                    ->options(
                                        array_combine(
                                            range(date('Y'), 1920),
                                            range(date('Y'), 1920),
                                        )
                                    )
                                    ->in(range(date('Y'), 1920))
                                    ->required()
                                    ->searchable(),
                                Forms\Components\Select::make('city')
                                    ->label(__('resources.car.fields.city'))
                                    ->enum(City::class)
                                    ->options(City::class)
                                    ->required()
                                    ->searchable(),
                                Forms\Components\Select::make('status')
                                    ->label(__('resources.car.fields.status'))
                                    ->enum(Status::class)
                                    ->options(Status::class)
                                    ->required()
                                    ->live(),
                                Forms\Components\TextInput::make('travel_distance_in_km')
                                    ->label(__('resources.car.fields.kilometers'))
                                    ->required()
                                    ->numeric()
                                    ->minValue(0)
                                    ->dehydrated(),
                                Forms\Components\Toggle::make('has_warranty')
                                    ->label(__('resources.car.fields.has_warranty'))
                                    ->default(false),
                            ])
                            ->columns(3),

                        Forms\Components\Wizard\Step::make(__('resources.car.steps.technical_specifications'))
                            ->id('technical_specifications')
                            ->icon('heroicon-o-cog')
                            ->schema([
                                Forms\Components\Select::make('cylinder_count')
                                    ->label(__('resources.car.fields.cylinder_count'))
                                    ->options(CylinderCount::class)
                                    ->enum(CylinderCount::class)
                                    ->required(),
                                Forms\Components\Select::make('regional_specifications')
                                    ->label(__('resources.car.fields.regional_specifications'))
                                    ->enum(RegionalSpecification::class)
                                    ->options(RegionalSpecification::class)
                                    ->required()
                                    ->searchable(),
                                Forms\Components\TextInput::make('engine_capacity')
                                    ->label(__('resources.car.fields.engine_capacity'))
                                    ->suffix('CC')
                                    ->required()
                                    ->numeric()
                                    ->minValue(50)
                                    ->maxValue(16400),
                                Forms\Components\TextInput::make('horse_power')
                                    ->label(__('resources.car.fields.horse_power'))
                                    ->suffix(__('resources.car.fields.horse_power_suffix'))
                                    ->numeric()
                                    ->minValue(4)
                                    ->maxValue(2000),
                            ])
                            ->columns(3),

                        Forms\Components\Wizard\Step::make(__('resources.car.steps.body_details'))
                            ->id('body_details')
                            ->icon('heroicon-o-cube')
                            ->schema([
                                Forms\Components\Select::make('body_type')
                                    ->label(__('resources.car.fields.body_type'))
                                    ->enum(BodyType::class)
                                    ->options(BodyType::class)
                                    ->required()
                                    ->live(),
                                Forms\Components\Select::make('seats_count')
                                    ->label(__('resources.car.fields.seats_count'))
                                    ->suffix(__('resources.car.fields.seats_count_suffix'))
                                    ->options(function (Forms\Get $get) {
                                        /** @var ?string $bodyTypeValue */
                                        $bodyTypeValue = $get('body_type');

                                        if (! $bodyTypeValue) {
                                            return [];
                                        }

                                        $bodyType = BodyType::from($bodyTypeValue);
                                        $options = $bodyType->getSeatsCounts();

                                        return array_combine($options, $options);
                                    })
                                    ->required(),
                                Forms\Components\Select::make('exterior_color')
                                    ->label(__('resources.car.fields.exterior_color'))
                                    ->options(ExteriorColor::class)
                                    ->enum(ExteriorColor::class)
                                    ->required(),
                                Forms\Components\Select::make('interior_color')
                                    ->label(__('resources.car.fields.interior_color'))
                                    ->options(InteriorColor::class)
                                    ->enum(InteriorColor::class)
                                    ->required(),
                            ])
                            ->columns(2),

                        Forms\Components\Wizard\Step::make(__('resources.car.steps.drivetrain'))
                            ->id('drivetrain')
                            ->icon('heroicon-o-wrench')
                            ->schema([
                                Forms\Components\Select::make('fuel_type')
                                    ->label(__('resources.car.fields.fuel_type'))
                                    ->enum(FuelType::class)
                                    ->options(FuelType::class)
                                    ->required(),
                                Forms\Components\Select::make('transmission')
                                    ->label(__('resources.car.fields.transmission'))
                                    ->enum(TransmissionType::class)
                                    ->options(TransmissionType::class)
                                    ->required(),
                                Forms\Components\Select::make('drivetrain_system')
                                    ->label(__('resources.car.fields.drivetrain_system'))
                                    ->enum(DrivetrainSystem::class)
                                    ->options(DrivetrainSystem::class)
                                    ->required(),
                            ])
                            ->columns(3),

                        Forms\Components\Wizard\Step::make(__('resources.car.steps.features'))
                            ->id('features')
                            ->icon('heroicon-o-sparkles')
                            ->schema(
                                function () {
                                    /** @var Collection<int, FeatureCategory> $categories */
                                    $categories = Feature::select('category')
                                        ->distinct()
                                        ->pluck('category');

                                    return $categories
                                        ->map(
                                            fn (FeatureCategory $category) => Forms\Components\Section::make(__('enums.feature_category.' . $category->value))
                                                ->schema([
                                                    Forms\Components\CheckboxList::make('features')
                                                        ->hiddenLabel()
                                                        ->relationship(
                                                            titleAttribute: 'name_' . app()->getLocale(),
                                                            modifyQueryUsing: fn (Builder $query): Builder => $query->where('category', $category)->orderBy('id')
                                                        )
                                                        ->columns(3),
                                                ])
                                        )
                                        ->toArray();
                                }
                            )
                            ->columns(1),

                        Forms\Components\Wizard\Step::make(__('resources.car.steps.gallery'))
                            ->id('gallery')
                            ->icon('heroicon-o-camera')
                            ->schema([
                                Forms\Components\FileUpload::make('bulk_images')
                                    ->label(__('resources.car.fields.upload_multiple_images'))
                                    ->image()
                                    ->multiple()
                                    ->reorderable()
                                    ->panelLayout('grid')
                                    ->dehydrated(false)
                                    ->directory('temp-car-images')
                                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, $state) {
                                        if (empty($state)) {
                                            return;
                                        }

                                        $currentImages = $get('gallery') ?? [];
                                        $newImages = [];

                                        foreach ($state as $uploadedFile) {
                                            $newImages[] = [
                                                'temp_image_path' => $uploadedFile,
                                                'label' => null,
                                                'order' => count($currentImages) + count($newImages) + 1,
                                            ];
                                        }

                                        $set('gallery', array_merge($currentImages, $newImages));
                                        $set('bulk_images', []);
                                    })
                                    ->helperText(__('resources.car.fields.upload_multiple_images_help')),

                                Forms\Components\Repeater::make('gallery')
                                    ->columns(['md' => 2])
                                    ->relationship('images', fn (Builder $query) => $query->with('media'))
                                    ->label(__('resources.car.fields.images'))
                                    ->orderColumn('order')
                                    ->schema([
                                        Forms\Components\Hidden::make('temp_image_path'),
                                        Forms\Components\Select::make('label')
                                            ->label(__('resources.car.fields.image_label'))
                                            ->enum(ImageLabel::class)
                                            ->options(ImageLabel::class)
                                            ->live()
                                            ->columnSpanFull(),
                                        Forms\Components\ViewField::make('image_preview')
                                            ->label(__('resources.car.fields.image'))
                                            ->view('filament.forms.components.image-preview-with-upload')
                                            ->visible(fn (Forms\Get $get) => (bool) $get('temp_image_path'))
                                            ->dehydrated(false),
                                        Forms\Components\SpatieMediaLibraryFileUpload::make('image')
                                            ->label(__('resources.car.fields.image'))
                                            ->collection(Car::DEFAULT_IMAGES_COLLECTION_NAME)
                                            ->image()
                                            ->imageEditor()
                                            ->visible(fn (Forms\Get $get) => ! $get('temp_image_path')),
                                        Forms\Components\Placeholder::make('example')
                                            ->label(__('resources.car.fields.example'))
                                            ->dehydrated(false)
                                            ->visible(fn (Forms\Get $get) => $get('label'))
                                            ->content(function (Forms\Get $get) {
                                                /** @var string $label */
                                                $label = $get('label') ?? '';
                                                $imageLabel = ImageLabel::from($label);

                                                $imageSrc = $imageLabel->getExampleImageSrc();

                                                return new HtmlString(<<<HTML
                                                <img src="{$imageSrc}" alt="placeholder" class="w-full h-full" />
                                            HTML
                                                );
                                            }),
                                    ]),
                            ]),

                        Forms\Components\Wizard\Step::make(__('resources.car.steps.pricing'))
                            ->id('pricing')
                            ->icon('heroicon-o-currency-dollar')
                            ->schema([
                                Forms\Components\TextInput::make('price')
                                    ->label(__('resources.car.fields.price'))
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),
                                Forms\Components\Select::make('currency')
                                    ->label(__('resources.car.fields.currency'))
                                    ->enum(Currency::class)
                                    ->options(Currency::class)
                                    ->required(),
                            ])
                            ->columns(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('store.name_' . app()->getLocale())
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('carManufacturer.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('carModel.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('cylinder_count')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('regional_specifications')
                    ->searchable(),
                Tables\Columns\TextColumn::make('year')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('travel_distance_in_km')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('body_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('city')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fuel_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('transmission')
                    ->searchable(),
                Tables\Columns\TextColumn::make('drivetrain_system')
                    ->searchable(),
                Tables\Columns\TextColumn::make('seats_count')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('exterior_color')
                    ->searchable(),
                Tables\Columns\TextColumn::make('interior_color')
                    ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->numeric()
                    ->sortable()
                    ->money(fn (Car $record) => $record->currency->value),
                Tables\Columns\TextColumn::make('currency')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('engine_capacity')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('horse_power')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('has_warranty')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCars::route('/'),
            'create' => Pages\CreateCar::route('/create'),
            'edit' => Pages\EditCar::route('/{record}/edit'),
        ];
    }
}
