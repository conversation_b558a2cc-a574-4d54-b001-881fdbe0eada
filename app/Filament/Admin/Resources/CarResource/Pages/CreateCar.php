<?php

namespace App\Filament\Admin\Resources\CarResource\Pages;

use App\Filament\Admin\Resources\CarResource;
use App\Models\Car;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Storage;

class CreateCar extends CreateRecord
{
    protected static string $resource = CarResource::class;

    protected function afterCreate(): void
    {
        $this->processTemporaryImages();
    }

    protected function processTemporaryImages(): void
    {
        $data = $this->form->getState();

        if (isset($data['gallery']) && is_array($data['gallery'])) {
            foreach ($data['gallery'] as $imageData) {
                if (isset($imageData['temp_image_path']) && $imageData['temp_image_path']) {
                    // Create CarImage record
                    $carImage = $this->record->images()->create([
                        'label' => $imageData['label'] ?? null,
                        'order' => $imageData['order'] ?? 0,
                    ]);

                    // Move temporary file to permanent location and create media record
                    $tempPath = $imageData['temp_image_path'];
                    if (Storage::exists($tempPath)) {
                        $carImage->addMediaFromDisk($tempPath, 'public')
                            ->toMediaCollection(Car::DEFAULT_IMAGES_COLLECTION_NAME);

                        // Clean up temporary file
                        Storage::delete($tempPath);
                    }
                }
            }
        }
    }
}
