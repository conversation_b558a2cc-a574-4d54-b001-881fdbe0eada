<?php

namespace App\Filament\Shared\Components;

use App\Enums\Car\ImageLabel;
use App\Models\Car;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\ViewField;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class BulkImageUpload extends Component
{
    protected string $view = 'filament.forms.components.bulk-image-upload';

    public static function make(string $name = 'gallery'): static
    {
        return app(static::class, ['name' => $name]);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->schema([
            FileUpload::make('bulk_images')
                ->label(__('resources.car.fields.upload_multiple_images'))
                ->image()
                ->multiple()
                ->reorderable()
                ->panelLayout('grid')
                ->dehydrated(false)
                ->directory('temp-car-images')
                ->afterStateUpdated(function ($set, $get, $state) {
                    if (empty($state)) {
                        return;
                    }

                    $currentImages = $get('gallery') ?? [];
                    $newImages = [];

                    foreach ($state as $uploadedFile) {
                        $newImages[] = [
                            'temp_image_path' => $uploadedFile,
                            'label' => null,
                            'order' => count($currentImages) + count($newImages) + 1,
                        ];
                    }

                    $set('gallery', array_merge($currentImages, $newImages));
                    $set('bulk_images', []);
                })
                ->helperText(__('resources.car.fields.upload_multiple_images_help')),

            Repeater::make('gallery')
                ->columns(['md' => 2])
                ->relationship('images', fn (Builder $query) => $query->with('media'))
                ->label(__('resources.car.fields.images'))
                ->orderColumn('order')
                ->schema([
                    Hidden::make('temp_image_path'),
                    Select::make('label')
                        ->label(__('resources.car.fields.image_label'))
                        ->enum(ImageLabel::class)
                        ->options(ImageLabel::class)
                        ->live()
                        ->columnSpanFull(),
                    ViewField::make('image_preview')
                        ->label(__('resources.car.fields.image'))
                        ->view('filament.forms.components.image-preview-with-upload')
                        ->visible(fn ($get) => (bool) $get('temp_image_path'))
                        ->dehydrated(false),
                    SpatieMediaLibraryFileUpload::make('image')
                        ->label(__('resources.car.fields.image'))
                        ->collection(Car::DEFAULT_IMAGES_COLLECTION_NAME)
                        ->image()
                        ->imageEditor()
                        ->visible(fn ($get) => ! $get('temp_image_path')),
                    Placeholder::make('example')
                        ->label(__('resources.car.fields.example'))
                        ->dehydrated(false)
                        ->visible(fn ($get) => $get('label'))
                        ->content(function ($get) {
                            /** @var string $label */
                            $label = $get('label') ?? '';
                            $imageLabel = ImageLabel::from($label);

                            $imageSrc = $imageLabel->getExampleImageSrc();

                            return new HtmlString(<<<HTML
                                <img src="{$imageSrc}" alt="placeholder" class="w-full h-full" />
                            HTML
                            );
                        }),
                ]),
        ]);
    }
}
