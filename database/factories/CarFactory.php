<?php

namespace Database\Factories;

use App\Enums\Car\BodyType;
use App\Enums\Car\DrivetrainSystem;
use App\Enums\Car\FuelType;
use App\Enums\Car\RegionalSpecification;
use App\Enums\Car\Status;
use App\Enums\Car\TransmissionType;
use App\Enums\City;
use App\Enums\Currency;
use App\Models\Car;
use App\Models\CarManufacturer;
use App\Models\CarModel;
use App\Models\Store;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Car>
 */
class CarFactory extends Factory
{
    protected $model = Car::class;

    public function definition(): array
    {
        return [
            'store_id' => Store::factory(),
            'car_manufacturer_id' => CarManufacturer::factory(),
            'car_model_id' => CarModel::factory(),
            'cylinder_count' => $this->faker->randomElement(CylinderCount::cases())->value,
            'regional_specifications' => $this->faker->randomElement(RegionalSpecification::cases()),
            'year' => $this->faker->numberBetween(2000, 2024),
            'travel_distance_in_km' => $this->faker->numberBetween(0, 300000),
            'body_type' => $this->faker->randomElement(BodyType::cases()),
            'city' => $this->faker->randomElement(City::cases()),
            'fuel_type' => $this->faker->randomElement(FuelType::cases()),
            'transmission' => $this->faker->randomElement(TransmissionType::cases()),
            'drivetrain_system' => $this->faker->randomElement(DrivetrainSystem::cases()),
            'seats_count' => $this->faker->numberBetween(2, 8),
            'exterior_color' => $this->faker->randomElement(ExteriorColor::cases()),
            'interior_color' => $this->faker->randomElement(InteriorColor::cases()),
            'price' => $this->faker->numberBetween(10000, 100000),
            'currency' => $this->faker->randomElement(Currency::cases()),
            'status' => $this->faker->randomElement(Status::cases()),
            'engine_capacity' => $this->faker->numberBetween(1000, 6000),
            'horse_power' => $this->faker->numberBetween(100, 500),
            'has_warranty' => $this->faker->boolean(),
        ];
    }
}
