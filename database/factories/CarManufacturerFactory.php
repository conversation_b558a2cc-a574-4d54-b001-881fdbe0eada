<?php

namespace Database\Factories;

use App\Models\CarManufacturer;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CarManufacturer>
 */
class CarManufacturerFactory extends Factory
{
    protected $model = CarManufacturer::class;

    public function definition(): array
    {
        $name = $this->faker->randomElement(['Toyota', 'Honda', 'BMW', 'Mercedes', 'Audi', 'Ford', 'Chevrolet']);

        return [
            'name_ar' => $name,
            'name_en' => $name,
        ];
    }
}
