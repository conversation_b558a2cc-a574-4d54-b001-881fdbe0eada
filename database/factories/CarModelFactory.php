<?php

namespace Database\Factories;

use App\Models\CarManufacturer;
use App\Models\CarModel;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CarModel>
 */
class CarModelFactory extends Factory
{
    protected $model = CarModel::class;

    public function definition(): array
    {
        $name = $this->faker->randomElement(['Camry', 'Corolla', 'Civic', 'Accord', 'X5', 'C-Class', 'A4', 'F-150']);

        return [
            'car_manufacturer_id' => CarManufacturer::factory(),
            'name_ar' => $name,
            'name_en' => $name,
        ];
    }
}
