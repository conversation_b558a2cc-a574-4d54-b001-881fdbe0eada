@php
    $state = $getState();
    $tempImagePath = data_get($state, 'temp_image_path');
    $imageUrl = null;
    
    if ($tempImagePath) {
        $imageUrl = Storage::url($tempImagePath);
    }
@endphp

<div class="relative group">
    @if($imageUrl)
        <div class="relative overflow-hidden rounded-lg border border-gray-300 dark:border-gray-600">
            <img 
                src="{{ $imageUrl }}" 
                alt="Car image preview" 
                class="w-full h-48 object-cover"
            />
            
            <!-- Hover overlay -->
            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                <div class="text-center text-white">
                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <p class="text-sm font-medium">{{ __('Image uploaded') }}</p>
                </div>
            </div>
        </div>
    @else
        <div class="w-full h-48 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center">
            <div class="text-center">
                <svg class="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">{{ __('No image uploaded') }}</p>
            </div>
        </div>
    @endif
</div>
