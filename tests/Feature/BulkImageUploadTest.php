<?php

namespace Tests\Feature;

use App\Models\Car;
use App\Models\CarManufacturer;
use App\Models\CarModel;
use App\Models\Store;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class BulkImageUploadTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user for authentication
        $user = User::factory()->create();
        $this->actingAs($user);

        // Set up storage for testing
        Storage::fake('public');
    }

    /** @test */
    public function it_can_create_car_images_from_temporary_uploads()
    {
        // Create necessary related models
        $store = Store::factory()->create();
        $manufacturer = CarManufacturer::factory()->create();
        $model = CarModel::factory()->create(['car_manufacturer_id' => $manufacturer->id]);

        // Create a car
        $car = Car::factory()->create([
            'store_id' => $store->id,
            'car_manufacturer_id' => $manufacturer->id,
            'car_model_id' => $model->id,
        ]);

        // Create fake uploaded files
        $file1 = UploadedFile::fake()->image('car1.jpg', 800, 600);
        $file2 = UploadedFile::fake()->image('car2.jpg', 800, 600);

        // Store files temporarily
        $tempPath1 = $file1->store('temp-car-images', 'public');
        $tempPath2 = $file2->store('temp-car-images', 'public');

        // Simulate the gallery data structure that would come from the form
        $galleryData = [
            [
                'temp_image_path' => $tempPath1,
                'label' => 'front_angle',
                'order' => 1,
            ],
            [
                'temp_image_path' => $tempPath2,
                'label' => 'rear_angle',
                'order' => 2,
            ],
        ];

        // Process the temporary images (simulating what happens in CreateCar/EditCar)
        foreach ($galleryData as $imageData) {
            if (isset($imageData['temp_image_path']) && $imageData['temp_image_path']) {
                $carImage = $car->images()->create([
                    'label' => $imageData['label'] ?? null,
                    'order' => $imageData['order'] ?? 0,
                ]);

                if (Storage::disk('public')->exists($imageData['temp_image_path'])) {
                    $carImage->addMediaFromDisk($imageData['temp_image_path'], 'public')
                        ->toMediaCollection(Car::DEFAULT_IMAGES_COLLECTION_NAME);

                    Storage::disk('public')->delete($imageData['temp_image_path']);
                }
            }
        }

        // Assert that car images were created
        $this->assertCount(2, $car->fresh()->images);

        // Assert that each image has the correct label and order
        $images = $car->fresh()->images()->orderBy('order')->get();
        $this->assertEquals('front_angle', $images[0]->label->value);
        $this->assertEquals('rear_angle', $images[1]->label->value);
        $this->assertEquals(1, $images[0]->order);
        $this->assertEquals(2, $images[1]->order);

        // Assert that media was attached
        $this->assertCount(1, $images[0]->getMedia(Car::DEFAULT_IMAGES_COLLECTION_NAME));
        $this->assertCount(1, $images[1]->getMedia(Car::DEFAULT_IMAGES_COLLECTION_NAME));

        // Assert that temporary files were cleaned up
        $this->assertFalse(Storage::disk('public')->exists($tempPath1));
        $this->assertFalse(Storage::disk('public')->exists($tempPath2));
    }
}
